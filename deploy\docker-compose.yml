# Docker Compose 配置文件

services:
  homepage-backend:
    build:
      context: ..
      dockerfile: deploy/Dockerfile
    container_name: homepage-backend
    restart: unless-stopped
    ports:
      - "127.0.0.1:5000:5000"  # 只绑定到本地，通过nginx代理
    volumes:
      # 持久化配置文件
      - ./data/config.json:/app/frontend/public/config.json
      # 持久化日志
      - ./logs:/app/logs
      # 前端静态文件（供nginx访问）
      - ../frontend:/var/www/homepage:ro  # 只读挂载，提高安全性
    environment:
      - FLASK_ENV=production
      - PYTHONPATH=/app/backend
      # 可选：从环境变量读取密码
      - ADMIN_PASSWORD=${ADMIN_PASSWORD:-LongDz6299}
      # Python优化
      - PYTHONUNBUFFERED=1
      - PYTHONDONTWRITEBYTECODE=1
    # 资源限制 - 根据你的服务器配置调整
    deploy:
      resources:
        limits:
          cpus: '0.5'      # 限制CPU使用，根据需要调整
          memory: 256M     # 限制内存使用，个人主页够用了
        reservations:
          cpus: '0.1'
          memory: 64M
    # 健康检查
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - homepage-network
    # 日志配置 - 限制日志大小
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

networks:
  homepage-network:
    driver: bridge

volumes:
  homepage-data:
