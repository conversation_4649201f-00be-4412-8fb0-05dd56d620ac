# 个人主页 Nginx 配置
# 请将此文件复制到 /etc/nginx/sites-available/ 并创建软链接到 sites-enabled
# 然后修改 server_name 为你的域名

server {
    listen 80;
    server_name your-domain.com;  # 请修改为你的域名
    
    # 获取真实IP（Cloudflare）
    real_ip_header CF-Connecting-IP;
    set_real_ip_from ************/20;
    set_real_ip_from ************/22;
    set_real_ip_from ************/22;
    set_real_ip_from **********/22;
    set_real_ip_from ************/18;
    set_real_ip_from *************/18;
    set_real_ip_from ************/20;
    set_real_ip_from ************/20;
    set_real_ip_from *************/22;
    set_real_ip_from ************/17;
    set_real_ip_from ***********/15;
    set_real_ip_from **********/13;
    set_real_ip_from **********/14;
    set_real_ip_from **********/13;
    set_real_ip_from **********/22;
    
    # 静态文件根目录
    root /var/www/homepage;
    index index.html;
    
    # 日志配置
    access_log /var/log/nginx/homepage_access.log;
    error_log /var/log/nginx/homepage_error.log warn;
    
    # 文件上传大小限制
    client_max_body_size 50M;
    
    # 安全头
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    
    # Gzip 压缩优化
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml
        application/x-font-ttf
        application/vnd.ms-fontobject
        font/opentype;

    # Brotli 压缩（如果nginx支持）
    # brotli on;
    # brotli_comp_level 6;
    # brotli_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

    # 连接优化
    keepalive_timeout 65;
    keepalive_requests 100;

    # 缓冲区优化
    client_body_buffer_size 128k;
    client_header_buffer_size 1k;
    large_client_header_buffers 4 4k;
    output_buffers 1 32k;
    postpone_output 1460;
    
    # API 接口代理到后端容器（性能优化）
    location /api/ {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # 支持WebSocket
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";

        # 超时设置优化
        proxy_connect_timeout 10s;
        proxy_send_timeout 10s;
        proxy_read_timeout 10s;

        # 缓冲区优化
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
        proxy_busy_buffers_size 8k;

        # 连接复用
        proxy_socket_keepalive on;

        # CORS headers
        add_header Access-Control-Allow-Origin * always;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
        add_header Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With" always;

        # 处理预检请求
        if ($request_method = 'OPTIONS') {
            add_header Access-Control-Allow-Origin * always;
            add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
            add_header Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With" always;
            add_header Access-Control-Max-Age 1728000;
            add_header Content-Type 'text/plain charset=UTF-8';
            add_header Content-Length 0;
            return 204;
        }

        # 请求频率限制（需要在 nginx.conf 中定义 limit_req_zone）
        # limit_req zone=api burst=20 nodelay;
    }
    
    # 登录API特殊限制
    location /api/login {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 登录频率限制（需要在 nginx.conf 中定义 limit_req_zone）
        # limit_req zone=login burst=5 nodelay;
    }
    
    # 管理后台
    location /admin {
        try_files $uri $uri/ /admin.html;
        
        # HTML 不缓存
        location ~* \.html$ {
            expires -1;
            add_header Cache-Control "no-cache, must-revalidate";
            add_header Pragma "no-cache";
        }
    }
    
    # 应用图标
    location /icons/ {
        alias /var/www/homepage/icons/;
        expires 30d;
        add_header Cache-Control "public, immutable";
        access_log off;
    }
    
    # Favicon 特殊处理
    location = /favicon.ico {
        root /var/www/homepage;
        expires 30d;
        add_header Cache-Control "public, immutable";
        access_log off;
        error_log off;
    }
    
    # robots.txt
    location = /robots.txt {
        root /var/www/homepage;
        expires 1d;
        add_header Cache-Control "public";
        access_log off;
    }
    
    # 静态资源缓存优化
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
        
        # 启用压缩
        gzip_static on;
        
        # 字体文件跨域
        location ~* \.(woff|woff2|ttf|eot|otf)$ {
            add_header Access-Control-Allow-Origin "*";
        }
    }
    
    # React Router 支持 - 处理前端路由
    location / {
        try_files $uri $uri/ /index.html;
        
        # HTML 不缓存，确保更新及时
        location ~* \.html$ {
            expires -1;
            add_header Cache-Control "no-cache, must-revalidate";
            add_header Pragma "no-cache";
        }
        
        # 启用压缩
        gzip_static on;
    }
    
    # 健康检查端点
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
    
    # 安全限制 - 禁止访问敏感文件
    location ~ /\.(ht|git|svn|env) {
        deny all;
        return 404;
    }
    
    # 禁止访问配置文件
    location ~* \.(conf|config|ini|log|bak|backup|old|tmp)$ {
        deny all;
        return 404;
    }
    
    # 禁止访问隐藏目录
    location ~ ^/(\.well-known|config|logs|temp|cache|backup)/ {
        deny all;
        return 404;
    }
    
    # 限制请求方法
    if ($request_method !~ ^(GET|HEAD|POST|PUT|DELETE|OPTIONS)$) {
        return 405;
    }
    
    # 自定义错误页面
    error_page 404 /404.html;
    error_page 403 /403.html;
    error_page 500 502 503 504 /50x.html;
    
    location = /404.html {
        root /var/www/homepage;
        internal;
    }
    
    location = /403.html {
        root /var/www/homepage;
        internal;
    }
    
    location = /50x.html {
        root /var/www/homepage;
        internal;
    }
}

# HTTPS 配置（如果有 SSL 证书）
# server {
#     listen 443 ssl http2;
#     server_name your-domain.com;
#     
#     ssl_certificate /path/to/your/fullchain.pem;
#     ssl_certificate_key /path/to/your/privkey.pem;
#     
#     # SSL 配置
#     ssl_protocols TLSv1.2 TLSv1.3;
#     ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
#     ssl_prefer_server_ciphers off;
#     ssl_session_cache shared:SSL:10m;
#     ssl_session_timeout 10m;
#     
#     # HSTS
#     add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
#     
#     # 其他配置与 HTTP 相同，复制上面的 location 块...
# }
