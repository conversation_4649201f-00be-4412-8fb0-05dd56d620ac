#!/bin/bash

# 更新现有nginx配置以支持个人主页项目
# 适用于已有nginx服务器

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置变量
DOMAIN="name666.top"
NGINX_SITE_NAME="home.name666.top"
NGINX_CONFIG="/etc/nginx/sites-available/$NGINX_SITE_NAME"
WEB_DIR="/var/www/homepage"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        exit 1
    fi
}

# 备份现有配置
backup_config() {
    if [[ -f "$NGINX_CONFIG" ]]; then
        local backup_file="${NGINX_CONFIG}.backup.$(date +%Y%m%d_%H%M%S)"
        cp "$NGINX_CONFIG" "$backup_file"
        log_success "已备份现有配置到: $backup_file"
    fi
}

# 检查配置中是否已有API代理
check_api_proxy() {
    if [[ -f "$NGINX_CONFIG" ]] && grep -q "location /api/" "$NGINX_CONFIG"; then
        log_warn "检测到配置中已有 /api/ 代理设置"
        return 0
    fi
    return 1
}

# 添加API代理配置
add_api_proxy() {
    log_info "添加API代理配置..."
    
    # 创建临时配置片段
    cat > /tmp/api_proxy.conf << 'EOF'
    
    # 个人主页项目API代理
    location /api/ {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时设置
        proxy_connect_timeout 10s;
        proxy_send_timeout 10s;
        proxy_read_timeout 10s;
        
        # 缓冲区优化
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
    }
EOF
    
    # 在最后一个 } 之前插入API代理配置
    sed -i '/^}$/i\
    # 个人主页项目API代理\
    location /api/ {\
        proxy_pass http://127.0.0.1:5000;\
        proxy_set_header Host $host;\
        proxy_set_header X-Real-IP $remote_addr;\
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;\
        proxy_set_header X-Forwarded-Proto $scheme;\
        \
        # 超时设置\
        proxy_connect_timeout 10s;\
        proxy_send_timeout 10s;\
        proxy_read_timeout 10s;\
        \
        # 缓冲区优化\
        proxy_buffering on;\
        proxy_buffer_size 4k;\
        proxy_buffers 8 4k;\
    }' "$NGINX_CONFIG"
    
    rm -f /tmp/api_proxy.conf
    log_success "已添加API代理配置"
}

# 添加静态文件配置
add_static_config() {
    log_info "检查静态文件配置..."
    
    # 检查是否已有root指令指向我们的目录
    if grep -q "root $WEB_DIR" "$NGINX_CONFIG"; then
        log_warn "检测到已有静态文件配置"
        return
    fi
    
    # 添加静态文件配置
    sed -i "/server_name.*$DOMAIN/a\\
    \\
    # 个人主页静态文件\\
    root $WEB_DIR;\\
    index index.html;" "$NGINX_CONFIG"
    
    log_success "已添加静态文件配置"
}

# 部署前端文件
deploy_frontend() {
    log_info "部署前端文件..."
    
    # 创建Web目录
    mkdir -p "$WEB_DIR"
    
    # 复制前端文件
    if [[ -d "../frontend" ]]; then
        cp -r ../frontend/* "$WEB_DIR/"
        log_success "已复制前端文件"
    else
        log_warn "未找到前端文件目录，请手动复制"
    fi
    
    # 设置权限
    chown -R www-data:www-data "$WEB_DIR"
    chmod -R 755 "$WEB_DIR"
    
    log_success "前端文件部署完成"
}

# 测试nginx配置
test_nginx() {
    log_info "测试nginx配置..."
    
    if nginx -t; then
        log_success "Nginx配置测试通过"
        systemctl reload nginx
        log_success "Nginx重载成功"
    else
        log_error "Nginx配置测试失败"
        log_info "请检查配置文件: $NGINX_CONFIG"
        exit 1
    fi
}

# 显示配置信息
show_info() {
    echo ""
    log_success "🎉 Nginx配置更新完成！"
    echo ""
    log_info "配置文件: $NGINX_CONFIG"
    log_info "前端目录: $WEB_DIR"
    log_info "API代理: /api/ -> http://127.0.0.1:5000"
    echo ""
    log_info "访问地址:"
    log_info "主页: http://$DOMAIN"
    log_info "管理后台: http://$DOMAIN/admin"
    log_info "API健康检查: http://$DOMAIN/api/health"
    echo ""
    log_warn "请确保Docker后端容器正在运行:"
    log_info "cd /opt/homepage/deploy && sudo docker compose up -d"
    echo ""
}

# 主函数
main() {
    echo "🔧 更新Nginx配置以支持个人主页项目"
    echo "======================================="
    
    check_root
    
    if [[ ! -f "$NGINX_CONFIG" ]]; then
        log_error "未找到nginx配置文件: $NGINX_CONFIG"
        log_info "请确认配置文件路径是否正确"
        exit 1
    fi
    
    backup_config
    
    if ! check_api_proxy; then
        add_api_proxy
    fi
    
    add_static_config
    deploy_frontend
    test_nginx
    show_info
}

# 执行主函数
main "$@"
