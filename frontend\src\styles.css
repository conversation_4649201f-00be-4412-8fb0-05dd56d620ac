* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'FZSongKeBenXiuKaiS-L', '方正宋刻本秀楷 简 L', 'STSong', 'SimSun', serif;
}



body {
    line-height: 1.6;
    color: #b2b2b2;
    height: 100vh;
    font-family: 'HYQuanTangShiJ', 'STSong', 'SimSun', serif;
    background-color: #1a1f23;
    overflow: hidden;
    cursor: url('../assets/cursor.png'), auto;
}

@font-face {
    font-family: 'FZSongKeBenXiuKaiS-L';
    src: local('FZSongKeBenXiuKaiS-L'), local('方正宋刻本秀楷 简 L'), url('FZSongKeBenXiuKaiS-L.woff2') format('woff2'), url('FZSongKeBenXiuKaiS-L.woff') format('woff');
    font-display: swap;
}
@font-face {
    font-family: 'HYQuanTangShiJ';
    src: local('HYQuanTangShiJ'), url('../assets/HYQuanTangShiJ.ttf') format('truetype');
    font-display: swap;
}

/* 快速白色粒子点击特效 */
.click-particle {
    position: fixed;
    pointer-events: none;
    z-index: 9999;
    width: 2px;
    height: 2px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.9);
    animation: fastParticle 0.4s ease-out forwards;
}

@keyframes fastParticle {
    0% {
        transform: scale(1) translate(0, 0);
        opacity: 1;
    }
    100% {
        transform: scale(0.3) translate(var(--dx), var(--dy));
        opacity: 0;
    }
}

/* 不同元素的光标样式 */
button, .tab-button, .social-button, .project-card, .friend-card {
    cursor: url('../assets/cursor.png'), pointer !important;
}

a, .clickable {
    cursor: url('../assets/cursor.png'), pointer !important;
}

input, textarea, select {
    cursor: url('../assets/cursor.png'), text !important;
}

.music-player {
    cursor: url('../assets/cursor.png'), auto !important;
}

.container {
    display: flex;
    height: 100vh;
    position: relative;
}

/* 侧边栏收回/展开按钮 */
.sidebar-toggle {
    position: fixed;
    top: 30px;
    left: 30px;
    z-index: 1000;
    background: rgba(255, 255, 255, 0.15) !important;
    border: 2px solid rgba(255, 255, 255, 0.3) !important;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    color: #ffffff;
    font-size: 18px;
    cursor: pointer;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: center;
    title: "点击切换侧边栏 (ESC键)";
}

.sidebar-toggle::after {
    content: "ESC";
    position: absolute;
    bottom: -25px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 10px;
    color: rgba(255, 255, 255, 0.6);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.sidebar-toggle:hover::after {
    opacity: 1;
}

.sidebar-toggle:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 15px rgba(255, 255, 255, 0.3);
}

.sidebar-toggle i {
    transition: transform 0.3s ease;
}

.sidebar-toggle.active i {
    transform: rotate(90deg);
}

/* 中心标题 */
.center-title {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 500;
    color: #ffffff;
    text-align: center;
    opacity: 1;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.center-title h1 {
    font-size: 3rem;
    font-weight: 300;
    letter-spacing: 0.1em;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    margin: 0;
    background: linear-gradient(45deg, #ffffff, #b2b2b2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: titleGlow 3s ease-in-out infinite alternate;
}

@keyframes titleGlow {
    0% {
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    }
    100% {
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5), 0 0 20px rgba(255, 255, 255, 0.3);
    }
}

/* 当侧边栏展开时隐藏中心标题 */
.center-title.hidden {
    opacity: 0;
    pointer-events: none;
}

.profile-section {
    width: 100%;
    max-width: none;
    background: transparent;
    padding: 80px 5px 20px 60px;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 2;
    overflow: hidden;
    height: 100vh;
    transform: translateX(0);
    transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    clip-path: polygon(0 0, 25% 0, 42.857% 100%, 0 100%);
    pointer-events: none;
}

/* 个人信息区域的毛玻璃背景层 */
.profile-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg,
        rgba(26, 31, 35, 0.8) 0%,
        rgba(26, 31, 35, 0.6) 50%,
        rgba(26, 31, 35, 0.7) 100%);
    z-index: -1;
    box-shadow: inset -1px 0 0 rgba(255, 255, 255, 0.1);
    transition: backdrop-filter 0.3s ease, -webkit-backdrop-filter 0.3s ease;
}

/* 启用毛玻璃效果时的样式 */
.profile-section.glass-morphism::before {
    backdrop-filter: blur(20px) saturate(1.3) brightness(1.1);
    -webkit-backdrop-filter: blur(20px) saturate(1.3) brightness(1.1);
    clip-path: polygon(0 0, 25% 0, 42.857% 100%, 0 100%);
}

/* 个人信息区域的光影增强层 */
.profile-section::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        rgba(255, 255, 255, 0.05) 0%,
        rgba(255, 255, 255, 0.02) 50%,
        transparent 100%);
    z-index: -1;
    pointer-events: none;
}

/* 收回状态 */
.profile-section.collapsed {
    transform: translateX(-100%);
}

.profile-info {
    max-width: 400px;
    width: 23%;
    margin: 0;
    color: #b2b2b2;
    background-color: transparent;
    pointer-events: auto;
    position: relative;
    z-index: 1;
    height: 100vh;
    padding: 20px 0;
    box-sizing: border-box;
    overflow: hidden;
}

.main-content {
    height: calc(100vh - 120px);
    overflow-y: auto;
    padding-bottom: 20px;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE/Edge */
}

.main-content::-webkit-scrollbar {
    display: none; /* Chrome/Safari */
}

.footer-info {
    position: absolute;
    bottom: 30px;
    left: 0;
    right: 0;
    padding: 0 20px;
}

.top-nav {
    display: flex;
    margin-bottom: 20px;
    padding-bottom: 2px;
}

.nav-item {
    flex: 1;
    text-align: center;
    color: #b2b2b2;
    text-decoration: none;
    padding: 8px 15px 0;
    position: relative;
    font-size: 1.5rem;
    transition: color 0.3s ease;
}

.nav-item::after {
    content: '';
    position: absolute;
    bottom: -3px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 3px;
    background: linear-gradient(to right, transparent, #f0c25d, transparent);
    transition: width 0.3s ease;
}

.nav-item.active::after,
.nav-item:hover::after {
    width: 100%;
}

.nav-item.active i,
.nav-item:hover i {
    color: #f0c25d;
}

.profile-header {
    display: flex;
    position: relative;
    margin-bottom: 30px;
    background-color: transparent;
    border-bottom: 1px solid #333;
    padding-bottom: 15px;
}

.header-title {
    position: absolute;
    top: -30px;
    left: 0;
    display: flex;
    align-items: center;
    font-size: 1.5rem;
    color: #b2b2b2;
}

.header-title::after {
    content: "";
    display: inline-block;
    width: 5px;
    height: 5px;
    border-radius: 50%;
    background-color: #f0c25d;
    margin: 0 10px;
}

.avatar {
    width: 90px;
    height: 90px;
    position: relative;
    margin-right: 15px;
}

.avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border: 1px solid #333;
}

.header-info {
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.name {
    font-size: 1.4rem;
    font-weight: 700;
    margin-bottom: 5px;
    color: #b2b2b2;
    text-align: left;
}

.title {
    font-size: 1.1rem;
    margin-bottom: 5px;
    color: #b2b2b2;
    text-align: left;
}

.birthday {
    font-size: 1rem;
    color: #b2b2b2;
    text-align: left;
}

.description {
    font-size: 1.1rem;
    line-height: 1.8;
    margin-bottom: 20px;
    text-align: justify;
    color: #b2b2b2;
    padding-bottom: 15px;
}

.social-media-buttons {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 20px;
}

.social-button {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.1);
    color: #ffffff;
    text-decoration: none;
    padding: 2px 5px;
    border-radius: 0;
    border: none;
    transition: all 0.3s ease;
}

.social-button:hover {
    background: rgba(255, 255, 255, 0.25);
    transform: translateY(-2px);
}

.social-button i {
    margin-right: 8px;
    font-size: 1.1rem;
}

.skills {
    margin-bottom: 20px;
    padding-bottom: 15px;
}

.skills h3 {
    font-size: 1.1rem;
    margin-bottom: 15px;
    font-weight: 600;
    color: #b2b2b2;
    text-align: center;
}

.skill-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    justify-content: space-between;
    box-sizing: border-box;
}

/* 技能方框样式，支持图标+文字 */
.skill-tag {
    width: calc(25% - 6px);
    height: 60px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background-color: #252a30;
    border: 1.4px solid rgba(178, 178, 178, 0.3);
    border-radius: 0;
    box-shadow: inset 0 0 6px 0 rgba(80,80,80,0.18),
                inset 0 0 20px rgba(0, 0, 0, 0.3),
                0 2px 4px rgba(0, 0, 0, 0.4);
    box-sizing: border-box;
    padding: 3px;
    font-size: 0.9rem;
    color: #b2b2b2;
    transition: all 0.2s ease;
    gap: 4px;
    position: relative;
    overflow: hidden;
}

/* 做旧效果 - 添加纹理和磨损感 */
.skill-tag::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 30%, rgba(0, 0, 0, 0.1) 1px, transparent 1px),
        radial-gradient(circle at 80% 70%, rgba(0, 0, 0, 0.08) 1px, transparent 1px),
        radial-gradient(circle at 40% 80%, rgba(255, 255, 255, 0.03) 1px, transparent 1px),
        linear-gradient(45deg, transparent 40%, rgba(0, 0, 0, 0.05) 50%, transparent 60%);
    background-size: 8px 8px, 12px 12px, 6px 6px, 100% 100%;
    pointer-events: none;
    opacity: 0.6;
}

/* 做旧效果 - 边缘磨损 */
.skill-tag::after {
    content: '';
    position: absolute;
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px;
    background: linear-gradient(45deg,
        rgba(0, 0, 0, 0.1) 0%,
        transparent 20%,
        transparent 80%,
        rgba(0, 0, 0, 0.1) 100%);
    pointer-events: none;
    opacity: 0.4;
}
.skill-tag i {
    font-size: 1.8rem;
    margin-bottom: 2px;
}
.skill-tag span {
    font-size: 0.85rem;
    margin: 0;
    line-height: 1;
}

/* 技能熟练度配色 */
/* 了解级别 */
.skill-tag[data-level="了解"] {
    background-color: #6f8a74;
    border-color: #5a7060;
    box-shadow: inset 0 0 6px 0 rgba(80,80,80,0.18),
                inset 0 0 20px rgba(0, 0, 0, 0.4),
                0 2px 4px rgba(0, 0, 0, 0.4);
}

.skill-tag[data-level="了解"]:hover {
    background-color: #7a9580;
    border-color: #647a68;
}

/* 熟悉级别 */
.skill-tag[data-level="熟悉"] {
    background-color: #bb8d29;
    border-color: #967021;
    box-shadow: inset 0 0 6px 0 rgba(80,80,80,0.18),
                inset 0 0 20px rgba(0, 0, 0, 0.4),
                0 2px 4px rgba(0, 0, 0, 0.4);
}

.skill-tag[data-level="熟悉"]:hover {
    background-color: #c99a35;
    border-color: #a37b2a;
}

/* 掌握级别 */
.skill-tag[data-level="掌握"] {
    background-color: #c66646;
    border-color: #9e5138;
    box-shadow: inset 0 0 6px 0 rgba(80,80,80,0.18),
                inset 0 0 20px rgba(0, 0, 0, 0.4),
                0 2px 4px rgba(0, 0, 0, 0.4);
}

.skill-tag[data-level="掌握"]:hover {
    background-color: #d17252;
    border-color: #a85c42;
}

/* 默认样式（保持原有效果） */
.skill-tag:hover {
    background-color: #2c3138;
    border-color: #f0c25d;
}

/* Tab内容样式 */
.tab-content {
    width: 100%;
}

/* 友链样式 */

/* ✅ 外层：固定高度，只显示5个卡片 */
.friend-links-wrapper {
    height: calc(5 * (72px + 16px)); /* 5个卡片高度 + 间距 */
    overflow: hidden;
    position: relative;
}

/* ✅ 内层：可滚动容器，隐藏滚动条 */
.friend-links {
    height: 65%;
    overflow-y: scroll;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE/Edge */
}

.friend-links::-webkit-scrollbar {
    display: none; /* Chrome/Safari */
}

/* ✅ 其余友链样式保持不变 */
.links-container {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 200px);
}

.links-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #b2b2b2;
    text-align: left;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 1px solid #333;
}

.friend-link {
    display: flex;
    align-items: center;
    background: linear-gradient(45deg, rgba(0, 0, 0, 0.6) 0%, rgba(0, 0, 0, 0.2) 100%);
    border: none;
    border-radius: 0;
    padding: 12px 15px;
    text-decoration: none;
    color: #ffffff;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    height: 90px;
    box-sizing: border-box;
    margin-bottom: 16px;
}

.friend-link:last-child {
    margin-bottom: 0;
}

.friend-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(240, 194, 93, 0.1), transparent);
    transition: left 0.5s ease;
}

.friend-link:hover::before {
    left: 100%;
}

.friend-link:hover {
    background: linear-gradient(45deg, rgba(0, 0, 0, 0.5) 0%, rgba(0, 0, 0, 0.1) 100%);
    transform: translateY(-2px);
}

.friend-link-icon {
    width: 32px;
    height: 32px;
    margin-right: 15px;
    border-radius: 0;
    object-fit: cover;
    border: 1px solid rgba(178, 178, 178, 0.3);
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* FontAwesome图标样式 */
i.friend-link-icon {
    width: auto;
    height: auto;
    font-size: 24px;
    object-fit: unset;
    border: none;
}

.friend-link-info {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.friend-link-name {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 2px;
    color: #ffffff;
}

.friend-link-desc {
    font-size: 0.85rem;
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.3;
}

.footer-info {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    padding-bottom: 18px;
    margin-top: 0;
    box-sizing: border-box;
    padding-left: 60px;
    padding-right: 20px;
}

.id-info {
    display: flex;
    align-items: center;
    font-size: 0.85rem;
    margin-bottom: 15px;
    color: #777;
}

.id-info i {
    margin-right: 8px;
}

.id-info span {
    margin-right: 15px;
}

.image-section {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    z-index: 1;
}

.background-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}

.background-video {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100vh !important;
    object-fit: cover !important;
    z-index: 1 !important;
    pointer-events: none !important;
}

/* 移除原来的背景层，现在由 profile-section 直接提供毛玻璃效果 */

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        flex-direction: column;
        overflow-y: auto;
    }
    
    .profile-section {
        width: 100%;
        max-width: none;
        margin: 0;
        height: auto;
        min-height: 100vh;
        padding: 20px;
        border-right: none;
        border-bottom: 1px solid rgba(178, 178, 178, 0.3);
    }

    .profile-section.collapsed {
        transform: translateY(-100%);
    }

    .sidebar-toggle {
        top: 20px;
        left: 20px;
        width: 45px;
        height: 45px;
        font-size: 16px;
    }

    .center-title h1 {
        font-size: 2rem;
    }
    
    .profile-header {
        flex-direction: column;
        align-items: center;
    }
    
    .avatar {
        margin-right: 0;
        margin-bottom: 15px;
    }
    
    .name, .title, .birthday {
        text-align: center;
    }
    
    .image-section {
        display: none;
    }
    
    .container::before {
        display: none;
    }
    
    .name {
        font-size: 2rem;
    }
    
    .profile-info {
        max-width: 100%;
    }
    
    .stats {
        justify-content: center;
        gap: 20px;
    }
    
    .menu-buttons {
        justify-content: center;
    }
    
    .action-buttons {
        flex-direction: column;
        gap: 10px;
    }
    
    .action-button {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .profile-section {
        padding: 15px;
    }
    
    .name {
        font-size: 1.1rem;
    }
    
    .title {
        font-size: 0.9rem;
    }
    
    .skill-tags {
        justify-content: center;
    }

    .links-container {
        height: auto;
    }

    .friend-links {
        max-height: 60vh;
    }

    .friend-link {
        padding: 10px 12px;
        height: 50px;
    }

    .friend-link-icon {
        width: 28px;
        height: 28px;
        margin-right: 12px;
    }

    .friend-link-name {
        font-size: 0.9rem;
    }

    .friend-link-desc {
        font-size: 0.8rem;
    }
}

/* 滚动条样式 */
.profile-section::-webkit-scrollbar {
    width: 6px;
}

.profile-section::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
}

.profile-section::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
}

.profile-section::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

/* 项目展示区域滚动和高度限制，和友链一致 */
.projects-container {
    display: flex;
    flex-direction: column;
    gap: 16px;
    max-height: calc(5 * (90px + 16px)); /* 5个卡片 */
    overflow-y: auto;
    overflow-x: hidden;
    scrollbar-width: none;
    -ms-overflow-style: none;
}
.projects-container::-webkit-scrollbar {
    display: none;
}

/* 项目卡片样式 */
.project-link {
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.05);
    border: none;
    border-radius: 4px;
    padding: 8px 14px;
    position: relative;
    min-height: 32px;
    box-sizing: border-box;
    color: #ffffff;
    transition: all 0.3s ease;
    margin-bottom: 0;
    text-decoration: none;
}
.project-link:hover {
    background: rgba(255, 255, 255, 0.25);
    transform: translateY(-2px);
}
.project-link-icon {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 24px;
    font-size: 2.2rem;
    border: 2px solid rgba(255, 255, 255, 0.4);
    flex-shrink: 0;
    color: #ffffff;
}
.project-link-info {
    display: flex;
    flex-direction: column;
    flex: 1;
}
.project-link-title {
    font-size: 1.25rem;
    color: #ffffff;
    font-weight: normal;
    margin-bottom: 6px;
}
.project-link-desc {
    font-size: 1rem;
    color: #ffffff;
    opacity: 0.85;
}
.project-link-arrow {
    margin-left: 18px;
    color: #ffffff;
    font-size: 1.3rem;
    align-self: center;
}

.projects-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #b2b2b2;
    text-align: left;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 1px solid #333;
}

/* 激光高亮边框效果 */
.social-button:hover,
.skill-tag:hover,
.friend-link:hover,
.project-link:hover,
button:hover,
a:hover,
.card:hover,
.avatar:hover,
.title:hover,
.description:hover {
    box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.8),
                0 0 8px rgba(255, 255, 255, 0.4),
                0 0 5px rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.9);
    transform: translateY(-2px);
    transition: all 0.3s ease;
}

/* 名字特殊激光效果 - 按内容长度 */
.name:hover {
    box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.8),
                0 0 8px rgba(255, 255, 255, 0.4),
                0 0 5px rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.9);
    transform: translateY(-2px);
    transition: all 0.3s ease;
    display: inline-block;
    width: fit-content;
    max-width: fit-content;
    padding: 2px 4px;
}

/* 移除导航项的激光效果 */
.nav-item:hover {
    box-shadow: none !important;
    border: none !important;
    transform: none !important;
}

/* 播放器特殊激光效果 - 只有上下边框 */
.music-player:hover {
    box-shadow: 0 -1px 0 0 rgba(255, 255, 255, 0.8),
                0 1px 0 0 rgba(255, 255, 255, 0.8),
                0 -8px 8px 0 rgba(255, 255, 255, 0.4),
                0 8px 8px 0 rgba(255, 255, 255, 0.4),
                0 -10px 10px 0 rgba(255, 255, 255, 0.2),
                0 10px 10px 0 rgba(255, 255, 255, 0.2);
    border-top: 1px solid rgba(255, 255, 255, 0.9);
    border-bottom: 1px solid rgba(255, 255, 255, 0.9);
    border-left: none !important;
    border-right: none !important;
    transition: all 0.3s ease;
}

/* 音乐播放器样式 */
.music-player {
    position: fixed;
    bottom: 100px;
    right: 0;
    width: 300px;
    height: 60px;
    background: linear-gradient(to right, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.3) 30%, rgba(0, 0, 0, 0.7) 100%);
    border: none;
    border-radius: 0;
    display: flex;
    align-items: center;
    padding: 10px 45px;
    z-index: 1000;
    transition: all 0.3s ease;
}

.music-player:hover {
    background: linear-gradient(to right, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.4) 30%, rgba(0, 0, 0, 0.8) 100%);
    cursor: pointer;
}

.vinyl-container {
    width: 40px;
    height: 40px;
    margin-right: 15px;
    position: relative;
    flex-shrink: 0;
}

.vinyl-record {
    width: 100%;
    height: 100%;
    background: #333;
    border-radius: 50%;
    position: relative;
    transition: transform 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.vinyl-record.spinning {
    animation: spin 3s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.vinyl-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    height: 100%;
    border-radius: 50%;
    overflow: hidden;
    border: 2px solid #666;
}

.album-cover {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

.music-info {
    flex: 1;
    display: flex;
    align-items: center;
    min-width: 0;
    height: 100%;
}

.song-text {
    font-size: 1rem;
    color: #ffffff;
    font-weight: 400;
    white-space: nowrap;
    overflow: hidden;
    position: relative;
    width: 100%;
    line-height: 1.2;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.song-text.scrolling {
    animation: scrollText 15s linear infinite;
}

.song-text:not(.scrolling) {
    text-overflow: ellipsis;
}

@keyframes scrollText {
    0% { transform: translateX(0); }
    25% { transform: translateX(0); }
    75% { transform: translateX(-100%); }
    100% { transform: translateX(-100%); }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .music-player {
        width: 250px;
        height: 50px;
        bottom: 60px;
        right: 0;
        padding: 8px 12px;
        border-radius: 0;
    }

    .vinyl-container {
        width: 35px;
        height: 35px;
        margin-right: 12px;
    }

    .song-text {
        font-size: 0.9rem;
    }
}
