# 个人主页项目 - 后端Docker镜像（混合部署版）
FROM python:3.9-slim

# 设置环境变量
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y --no-install-recommends \
    curl \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# 创建非root用户
RUN useradd -r -s /bin/false -u 1000 appuser

# 复制requirements文件并安装依赖
COPY backend/requirements.txt /app/
RUN pip install --no-cache-dir -r requirements.txt \
    && pip install --no-cache-dir gunicorn gevent

# 复制后端代码
COPY backend/ /app/

# 创建日志目录
RUN mkdir -p /app/logs

# 设置权限
RUN chown -R appuser:appuser /app

# 切换到非root用户
USER appuser

# 暴露端口
EXPOSE 5000

# 健康检查
HEALTHCHECK --interval=60s --timeout=5s --start-period=10s --retries=2 \
    CMD curl -f http://localhost:5000/api/health || exit 1

# 启动命令
CMD ["gunicorn", \
    "--bind", "0.0.0.0:5000", \
    "--worker-class", "gevent", \
    "--workers", "2", \
    "--worker-connections", "1000", \
    "--timeout", "30", \
    "--keep-alive", "2", \
    "--max-requests", "1000", \
    "--max-requests-jitter", "100", \
    "--preload", \
    "--access-logfile", "/app/logs/gunicorn_access.log", \
    "--error-logfile", "/app/logs/gunicorn_error.log", \
    "--log-level", "warning", \
    "app:app"]
